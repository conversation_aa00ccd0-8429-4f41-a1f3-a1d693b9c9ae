import socket
import json
import socket
import cmd2
import getopt
import sys
import binascii
import os
from scapy.all import *
from scapy.contrib.automotive.uds_scan import *
from cryptography.hazmat.primitives import cmac
from cryptography.hazmat.primitives.ciphers import algorithms
from scapy.contrib.automotive.doip import DoIP
from scapy.main import load_contrib
from progress.bar import Bar
load_contrib("automotive.doip")
load_contrib('automotive.uds')
import socket
import time
import binascii


def create_doip_socket():
    # 创建 socket 对象（IPv4, TCP）
    server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    
    # 绑定 IP 地址和端口号（这里监听本地 13400 端口）
    server_socket.bind(('0.0.0.0', 13400))
    
    # 设置监听，最多可以有 5 个等待连接的客户端
    server_socket.listen(5)
    print("服务器正在监听 13400 端口...")
    client_socket, addr = server_socket.accept()
    print(f"接收到来自 {addr} 的连接")
    return client_socket
RED = "\033[1;31m"
BLUE = "\033[1;34m"
CYAN = "\033[1;36m"
WHITE = "\033[1;37m"
YELLOW = "\033[1;33m"
GREEN = "\033[1;32m"
RESET = "\033[1;0m"
BOLD = "\033[;1m"
REVERSE = "\033[;7m"

print(CYAN + 'DoIP> ' + RESET,end='')
print('send 1060 2761 -n 50000')

doip_s = create_doip_socket()
doip_header = DoIP(payload_type=0x8001, source_address=0xe80, target_address=0x1015)

seed = []
def change_session(doip_s):
    each = '1060'
    doip_header = DoIP(payload_type=0x8001, source_address=0xe80, target_address=0x1015)
    pkt =  doip_header / UDS(binascii.a2b_hex(each))
    doip_s.send(bytes(pkt))
    resp = doip_s.recv(65535)
    resp = DoIP(resp)
    while resp[DoIP].payload_type == 0x8002 or (resp[UDS].service == 0x7f and resp[UDS_NR].negativeResponseCode == 0x78):
            #print('hhhh',binascii.b2a_hex(raw(resp)).decode('utf-8'))
            resp = doip_s.recv(65535)
            resp = DoIP(resp)
    if resp[DoIP].payload_type == 0x8001:
        prtdata = binascii.b2a_hex(raw(resp)).decode('utf-8').upper()
        each = each.upper()
        printSend = ' '.join(each[i:i+2] for i in range(0, len(each), 2))
        recvData = ' '.join(prtdata[i:i+2] for i in range(24, len(prtdata), 2))
        print('S e n d:',"\033[31m"+printSend+"\033[0m")
        print('Receive:',"\033[32m"+recvData+"\033[0m")

change_session(doip_s)
i = 0
number = 50000

# Initialize progress bar
bar = None
prefill_done = False  # 标记是否已经预填充
if number >= 100:
    formatlen = str(len(str(number)))
    suffixstr = '%(index)'+formatlen+'d/%(max)'+formatlen+'d'
    bar = Bar('Processing', max=number, suffix=suffixstr)

while True:
    pkt =  doip_header / UDS(binascii.a2b_hex('2761'))
    doip_s.send(bytes(pkt))
    resp = doip_s.recv(65535)
    resp = DoIP(resp)

    while resp[DoIP].payload_type == 0x8002 or (resp[UDS].service == 0x7f and resp[UDS_NR].negativeResponseCode == 0x78):
            #print('hhhh',binascii.b2a_hex(raw(resp)).decode('utf-8'))
            resp = doip_s.recv(65535)
            resp = DoIP(resp)
    if resp and resp[UDS].service == 0x7f and resp[UDS].negativeResponseCode == 0x7f:
         change_session(doip_s)

    elif resp and resp[UDS].service == 0x67:
        rcv_data =  binascii.b2a_hex(resp[UDS].securitySeed).decode('utf-8')
        if bar is None:
            print(rcv_data)
        seed.append(rcv_data)
        i += 1

        # Update progress bar
        if bar is not None:
            bar.next()

        if i == number:
            break

# Finish progress bar
if bar is not None:
    bar.finish()

with open('seed.log','w') as f:
    for each in seed:
         f.writelines(each+'\n')

print("\n[+] All seed are Saved in the seed.log file")
print('[*] path: ',os.getcwd()+os.sep+'seed.log')